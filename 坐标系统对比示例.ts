/**
 * 屏幕坐标 vs Three.js坐标 对比示例
 */

// 1. 地理坐标（经纬度）
const beijingGeo = [116.397128, 39.916527]; // 北京天安门
const shanghaiGeo = [121.473701, 31.230416]; // 上海外滩

// 2. 屏幕坐标系统（2D）
class ScreenCoordinateSystem {
  // 屏幕坐标特点：
  // - 原点(0,0)在左上角
  // - X轴向右为正
  // - Y轴向下为正
  // - 单位：像素

  static convertGeoToScreen(geoCoord: [number, number]): [number, number] {
    const [lng, lat] = geoCoord;
    
    // 简单的线性投影（实际项目中用D3或其他投影库）
    const x = (lng - 100) * 10; // 假设100度为基准
    const y = (50 - lat) * 10;  // 假设50度为基准，注意这里是 50-lat
    
    return [x, y];
  }
}

// 3. Three.js坐标系统（3D）
class ThreeJSCoordinateSystem {
  // Three.js坐标特点：
  // - 原点(0,0,0)在世界中心
  // - X轴向右为正
  // - Y轴向上为正（与屏幕相反！）
  // - Z轴向外为正
  // - 单位：世界单位

  static convertScreenToThreeJS(screenCoord: [number, number]): [number, number, number] {
    const [x, y] = screenCoord;
    
    // 关键转换：Y轴翻转
    return [
      x,    // X轴保持不变
      -y,   // Y轴翻转：屏幕的向下变成Three.js的向上
      0     // Z轴：暂时设为0（平面）
    ];
  }

  static addDepth(coord: [number, number, number], depth: number): [number, number, number] {
    const [x, y, z] = coord;
    return [x, y, z + depth]; // 添加3D深度
  }
}

// 4. 完整转换示例
function completeCoordinateTransformation() {
  console.log('=== 坐标转换完整过程 ===');
  
  // 步骤1：地理坐标
  console.log('1. 地理坐标（经纬度）:');
  console.log('北京:', beijingGeo);
  console.log('上海:', shanghaiGeo);
  
  // 步骤2：转换为屏幕坐标
  const beijingScreen = ScreenCoordinateSystem.convertGeoToScreen(beijingGeo);
  const shanghaiScreen = ScreenCoordinateSystem.convertGeoToScreen(shanghaiGeo);
  
  console.log('\n2. 屏幕坐标（2D投影）:');
  console.log('北京:', beijingScreen);
  console.log('上海:', shanghaiScreen);
  
  // 步骤3：转换为Three.js坐标
  const beijingThree = ThreeJSCoordinateSystem.convertScreenToThreeJS(beijingScreen);
  const shanghaiThree = ThreeJSCoordinateSystem.convertScreenToThreeJS(shanghaiScreen);
  
  console.log('\n3. Three.js坐标（Y轴翻转）:');
  console.log('北京:', beijingThree);
  console.log('上海:', shanghaiThree);
  
  // 步骤4：添加3D深度
  const beijingWith3D = ThreeJSCoordinateSystem.addDepth(beijingThree, 30);
  const shanghaiWith3D = ThreeJSCoordinateSystem.addDepth(shanghaiThree, 30);
  
  console.log('\n4. 最终3D坐标（添加深度）:');
  console.log('北京:', beijingWith3D);
  console.log('上海:', shanghaiWith3D);
}

// 5. 在ThreeMap中的实际应用
class CoordinateExampleInThreeMap {
  static demonstrateThreeMapProcess() {
    console.log('\n=== ThreeMap中的坐标处理 ===');
    
    // 模拟ThreeMap.createArea方法中的关键代码
    const geoCoordinates = [
      [116.397128, 39.916527], // 北京
      [121.473701, 31.230416], // 上海
      [113.264434, 23.129162]  // 广州
    ];
    
    console.log('原始地理坐标:', geoCoordinates);
    
    // 模拟投影转换
    const projectedCoords = geoCoordinates.map(coord => {
      const [lng, lat] = coord;
      const x = (lng - 100) * 10;
      const y = (50 - lat) * 10;
      return [x, y];
    });
    
    console.log('投影后坐标:', projectedCoords);
    
    // 模拟Three.js Shape创建（注意Y轴翻转）
    const threeJSCoords = projectedCoords.map(([x, y]) => [x, -y]);
    console.log('Three.js坐标（Y轴翻转）:', threeJSCoords);
    
    // 这就是为什么ThreeMap代码中有这一行：
    // shape.moveTo(x, -y);  // 注意这个 -y
    console.log('\n关键代码解释：');
    console.log('shape.moveTo(x, -y); // -y 就是为了翻转Y轴');
  }
}

// 6. 坐标系统对比可视化
function visualizeCoordinateSystems() {
  console.log('\n=== 坐标系统可视化对比 ===');
  
  console.log('屏幕坐标系统:');
  console.log('(0,0) -----> X');
  console.log('  |');
  console.log('  |');
  console.log('  v');
  console.log('  Y');
  console.log('原点在左上角，Y向下');
  
  console.log('\nThree.js坐标系统:');
  console.log('  Y');
  console.log('  ^');
  console.log('  |');
  console.log('  |');
  console.log('(0,0) -----> X');
  console.log('原点在中心，Y向上');
  
  console.log('\n这就是为什么需要 -y 转换！');
}

// 运行示例
completeCoordinateTransformation();
CoordinateExampleInThreeMap.demonstrateThreeMapProcess();
visualizeCoordinateSystems();

/**
 * 总结：
 * 
 * 1. 地理坐标 → 屏幕坐标：通过地图投影（D3等）
 * 2. 屏幕坐标 → Three.js坐标：Y轴翻转（-y）
 * 3. 2D坐标 → 3D坐标：添加Z轴深度
 * 
 * 关键点：
 * - 屏幕Y轴向下，Three.js Y轴向上
 * - 所以需要 -y 转换
 * - Three.js增加了Z轴（深度）
 */
